# هذا ملف اختبار لتلوين الكود
import tkinter as tk
from tkinter import ttk, messagebox
import os

class TestClass:
    """كلاس للاختبار"""
    
    def __init__(self, name):
        self.name = name
        self.numbers = [1, 2, 3, 4, 5]
        self.text = "مرحبا بالعالم"
        
    def display_info(self):
        """عرض المعلومات"""
        print(f"الاسم: {self.name}")
        print(f"الأرقام: {self.numbers}")
        
        # حلقة تكرار
        for i in range(len(self.numbers)):
            if self.numbers[i] % 2 == 0:
                print(f"الرقم {self.numbers[i]} زوجي")
            else:
                print(f"الرقم {self.numbers[i]} فردي")
    
    def calculate_sum(self):
        """حساب المجموع"""
        total = sum(self.numbers)
        return total

def main():
    """الدالة الرئيسية"""
    # إنشاء كائن من الكلاس
    test_obj = TestClass("اختبار")
    
    # عرض المعلومات
    test_obj.display_info()
    
    # حساب المجموع
    result = test_obj.calculate_sum()
    print(f"المجموع الكلي: {result}")
    
    # اختبار الشروط
    if result > 10:
        print("المجموع أكبر من 10")
    elif result == 10:
        print("المجموع يساوي 10")
    else:
        print("المجموع أقل من 10")

if __name__ == "__main__":
    main()
