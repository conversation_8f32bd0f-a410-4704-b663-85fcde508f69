import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pygments import highlight
from pygments.lexers import PythonLexer
from pygments.formatters import get_formatter_by_name
import re

class PythonToolsApp:
    def __init__(self, root):
        self.root = root
        self.root.title("أدوات ملفات البايثون")
        self.root.geometry("1000x700")
        
        # إعداد ألوان تلوين الكود
        self.setup_syntax_colors()
        
        # إنشاء الإطار الرئيسي
        self.setup_ui()
        
    def setup_syntax_colors(self):
        """إعداد ألوان تلوين الكود"""
        self.syntax_colors = {
            'keyword': '#0000FF',      # أزرق للكلمات المفتاحية
            'string': '#008000',       # أخضر للنصوص
            'comment': '#808080',      # رمادي للتعليقات
            'number': '#FF0000',       # أحمر للأرقام
            'function': '#800080',     # بنفسجي للدوال
            'class': '#008080',        # تركوازي للكلاسات
            'builtin': '#FF8000',      # برتقالي للدوال المدمجة
        }
        
    def setup_ui(self):
        # إنشاء إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # القائمة الجانبية على اليمين
        right_frame = ttk.Frame(main_frame, width=200)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10))
        right_frame.pack_propagate(False)
        
        # عنوان القائمة
        ttk.Label(right_frame, text="الأدوات", font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        # أزرار القائمة
        ttk.Button(right_frame, text="فتح ملف Python", 
                  command=self.open_python_file, width=20).pack(pady=5, fill=tk.X)
        
        ttk.Separator(right_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # أدوات إضافية
        ttk.Button(right_frame, text="تلوين الكود", 
                  command=self.highlight_syntax, width=20).pack(pady=5, fill=tk.X)
        ttk.Button(right_frame, text="تحليل الكود", 
                  command=self.analyze_code, width=20).pack(pady=5, fill=tk.X)
        ttk.Button(right_frame, text="تنسيق الكود", 
                  command=self.format_code, width=20).pack(pady=5, fill=tk.X)
        
        # النافذة الرئيسية على اليسار
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # شريط المعلومات
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_label = ttk.Label(info_frame, text="لم يتم فتح أي ملف", 
                                   font=("Arial", 10))
        self.file_label.pack(anchor=tk.W)
        
        # منطقة النص مع شريط التمرير
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء منطقة النص
        self.text_area = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 11),
                                bg='#FFFFFF', fg='#000000')
        
        # إعداد tags للتلوين
        self.setup_text_tags()
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_area.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.text_area.xview)
        
        self.text_area.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.text_area.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        # متغير لحفظ مسار الملف الحالي
        self.current_file = None
        
    def setup_text_tags(self):
        """إعداد tags للتلوين"""
        for tag, color in self.syntax_colors.items():
            self.text_area.tag_configure(tag, foreground=color)
            
    def highlight_syntax(self):
        """تلوين الكود"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return
            
        content = self.text_area.get(1.0, tk.END)
        
        # مسح التلوين السابق
        for tag in self.syntax_colors.keys():
            self.text_area.tag_remove(tag, 1.0, tk.END)
        
        # تلوين الكلمات المفتاحية
        keywords = ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except', 
                   'finally', 'with', 'import', 'from', 'as', 'return', 'yield', 'lambda',
                   'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None', 'pass', 'break', 'continue']
        
        for keyword in keywords:
            self.highlight_pattern(r'\b' + keyword + r'\b', 'keyword')
        
        # تلوين النصوص
        self.highlight_pattern(r'"[^"]*"', 'string')
        self.highlight_pattern(r"'[^']*'", 'string')
        self.highlight_pattern(r'""".*?"""', 'string')
        self.highlight_pattern(r"'''.*?'''", 'string')
        
        # تلوين التعليقات
        self.highlight_pattern(r'#.*', 'comment')
        
        # تلوين الأرقام
        self.highlight_pattern(r'\b\d+\.?\d*\b', 'number')
        
        # تلوين الدوال المدمجة
        builtins = ['print', 'len', 'range', 'str', 'int', 'float', 'list', 'dict', 'tuple', 'set']
        for builtin in builtins:
            self.highlight_pattern(r'\b' + builtin + r'\b', 'builtin')
        
        # تلوين أسماء الدوال
        self.highlight_pattern(r'def\s+(\w+)', 'function', group=1)
        
        # تلوين أسماء الكلاسات
        self.highlight_pattern(r'class\s+(\w+)', 'class', group=1)
        
    def highlight_pattern(self, pattern, tag, group=0):
        """تلوين نمط معين في النص"""
        content = self.text_area.get(1.0, tk.END)
        
        for match in re.finditer(pattern, content, re.MULTILINE | re.DOTALL):
            start_pos = self.get_text_position(content, match.start(group))
            end_pos = self.get_text_position(content, match.end(group))
            self.text_area.tag_add(tag, start_pos, end_pos)
    
    def get_text_position(self, content, index):
        """تحويل فهرس النص إلى موضع في Text widget"""
        lines_before = content[:index].count('\n')
        line_start = content.rfind('\n', 0, index) + 1
        column = index - line_start
        return f"{lines_before + 1}.{column}"
        
    def open_python_file(self):
        """فتح ملف Python وعرض محتواه"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # مسح المحتوى السابق وإدراج الجديد
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(1.0, content)
                
                # تحديث معلومات الملف
                self.current_file = file_path
                filename = os.path.basename(file_path)
                self.file_label.config(text=f"الملف: {filename}")
                
                # تلوين الكود تلقائياً
                self.highlight_syntax()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
    
    def analyze_code(self):
        """تحليل الكود (وظيفة مستقبلية)"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة تحليل الكود قريباً")
    
    def format_code(self):
        """تنسيق الكود (وظيفة مستقبلية)"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة تنسيق الكود قريباً")

def main():
    root = tk.Tk()
    app = PythonToolsApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
