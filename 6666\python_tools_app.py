import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pygments import highlight
from pygments.lexers import PythonLexer
from pygments.formatters import get_formatter_by_name
import re
import subprocess
import threading
import requests
import json
import ast

class PythonToolsApp:
    def __init__(self, root):
        self.root = root
        self.root.title("أدوات ملفات البايثون")
        self.root.geometry("1000x700")
        
        # إعداد ألوان تلوين الكود
        self.setup_syntax_colors()
        
        # إنشاء الإطار الرئيسي
        self.setup_ui()
        
    def setup_syntax_colors(self):
        """إعداد ألوان تلوين الكود"""
        self.syntax_colors = {
            'keyword': '#569CD6',      # أزرق فاتح للكلمات المفتاحية (import, from, def, class)
            'string': '#CE9178',       # برتقالي فاتح للنصوص
            'comment': '#6A9955',      # أخضر للتعليقات
            'number': '#B5CEA8',       # أخضر فاتح للأرقام
            'function': '#DCDCAA',     # أصفر فاتح للدوال
            'class': '#4EC9B0',        # تركوازي للكلاسات
            'builtin': '#569CD6',      # أزرق للدوال المدمجة
            'operator': '#D4D4D4',     # رمادي فاتح للعمليات
            'bracket': '#FFD700',      # ذهبي للأقواس
            'variable': '#9CDCFE',     # أزرق فاتح للمتغيرات
        }
        
    def setup_ui(self):
        # إنشاء إطار رئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # القائمة الجانبية على اليمين
        right_frame = ttk.Frame(main_frame, width=200)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10))
        right_frame.pack_propagate(False)
        
        # عنوان القائمة
        ttk.Label(right_frame, text="الأدوات", font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        # أزرار القائمة
        ttk.Button(right_frame, text="فتح ملف Python",
                  command=self.open_python_file, width=20).pack(pady=5, fill=tk.X)

        ttk.Separator(right_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # زر إدارة المكتبات
        self.library_button = ttk.Button(right_frame, text="إدارة المكتبات",
                                        command=self.toggle_library_menu, width=20)
        self.library_button.pack(pady=5, fill=tk.X)

        # إطار القائمة الفرعية لإدارة المكتبات
        self.library_menu_frame = ttk.Frame(right_frame)
        self.library_menu_visible = False

        # أزرار القائمة الفرعية
        ttk.Button(self.library_menu_frame, text="تحليل وتثبيت المكتبات",
                  command=self.ai_analyze_and_install, width=18).pack(pady=2, fill=tk.X)
        ttk.Button(self.library_menu_frame, text="تصفح مكتبات خارجية",
                  command=self.browse_external_libraries, width=18).pack(pady=2, fill=tk.X)
        ttk.Button(self.library_menu_frame, text="حذف مكتبة المشروع",
                  command=self.uninstall_project_library, width=18).pack(pady=2, fill=tk.X)
        ttk.Button(self.library_menu_frame, text="عرض متطلبات المشروع",
                  command=self.show_project_requirements, width=18).pack(pady=2, fill=tk.X)
        
        # النافذة الرئيسية على اليسار
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # شريط المعلومات
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_label = ttk.Label(info_frame, text="لم يتم فتح أي ملف",
                                   font=("Arial", 10))
        self.file_label.pack(anchor=tk.W)

        # شريط حالة المتطلبات
        self.requirements_status_frame = ttk.Frame(info_frame)
        self.requirements_status_frame.pack(fill=tk.X, pady=(5, 0))

        self.requirements_status_label = ttk.Label(self.requirements_status_frame,
                                                  text="", font=("Arial", 9))
        self.requirements_status_label.pack(anchor=tk.W)
        
        # منطقة النص مع شريط التمرير
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء منطقة النص
        self.text_area = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 11),
                                bg='#1E1E1E', fg='#D4D4D4', insertbackground='#FFFFFF',
                                selectbackground='#264F78', selectforeground='#FFFFFF')
        
        # إعداد tags للتلوين
        self.setup_text_tags()
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_area.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.text_area.xview)
        
        self.text_area.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.text_area.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        # نافذة النتائج أسفل نافذة الكود
        self.results_frame = ttk.LabelFrame(left_frame, text="نتائج العمليات", height=200)
        self.results_frame.pack(fill=tk.BOTH, expand=False, pady=(10, 0))
        self.results_frame.pack_propagate(False)

        # منطقة نص النتائج
        results_text_frame = ttk.Frame(self.results_frame)
        results_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.results_text = tk.Text(results_text_frame, wrap=tk.WORD, font=("Consolas", 10),
                                   bg='#2D2D30', fg='#CCCCCC', height=10)

        # شريط تمرير للنتائج
        results_scrollbar = ttk.Scrollbar(results_text_frame, orient=tk.VERTICAL,
                                         command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)

        self.results_text.grid(row=0, column=0, sticky="nsew")
        results_scrollbar.grid(row=0, column=1, sticky="ns")

        results_text_frame.grid_rowconfigure(0, weight=1)
        results_text_frame.grid_columnconfigure(0, weight=1)

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.results_frame, variable=self.progress_var,
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=(0, 5))

        # إخفاء نافذة النتائج في البداية
        self.results_frame.pack_forget()

        # متغير لحفظ مسار الملف الحالي
        self.current_file = None
        
    def setup_text_tags(self):
        """إعداد tags للتلوين"""
        for tag, color in self.syntax_colors.items():
            self.text_area.tag_configure(tag, foreground=color)
            
    def highlight_syntax(self):
        """تلوين الكود باستخدام pygments"""
        if not self.current_file:
            return

        content = self.text_area.get(1.0, tk.END)

        # مسح التلوين السابق
        for tag in self.syntax_colors.keys():
            self.text_area.tag_remove(tag, 1.0, tk.END)

        try:
            # استخدام pygments للحصول على tokens
            lexer = PythonLexer()
            tokens = list(lexer.get_tokens(content))

            current_pos = 1.0

            for token_type, token_value in tokens:
                if token_value.strip():  # تجاهل المسافات الفارغة
                    # تحديد نوع التلوين حسب نوع الـ token
                    tag = self.get_tag_for_token(str(token_type))

                    if tag:
                        # حساب موضع النهاية
                        lines = token_value.count('\n')
                        if lines == 0:
                            current_line = current_pos.split('.')[0]
                            current_col = int(current_pos.split('.')[1])
                            end_pos = f"{current_line}.{current_col + len(token_value)}"
                        else:
                            new_line = int(current_pos.split('.')[0]) + lines
                            last_line_text = token_value.split('\n')[-1]
                            end_pos = f"{new_line}.{len(last_line_text)}"

                        # تطبيق التلوين
                        self.text_area.tag_add(tag, current_pos, end_pos)

                # تحديث الموضع الحالي
                current_pos = self.advance_position(current_pos, token_value)

        except Exception as e:
            # في حالة فشل pygments، استخدم التلوين البسيط
            self.simple_highlight()

    def get_tag_for_token(self, token_type):
        """تحويل نوع token إلى tag للتلوين"""
        token_str = str(token_type)

        if 'Keyword' in token_str:
            return 'keyword'
        elif 'String' in token_str or 'Literal.String' in token_str:
            return 'string'
        elif 'Comment' in token_str:
            return 'comment'
        elif 'Number' in token_str or 'Literal.Number' in token_str:
            return 'number'
        elif 'Name.Function' in token_str:
            return 'function'
        elif 'Name.Class' in token_str:
            return 'class'
        elif 'Name.Builtin' in token_str:
            return 'builtin'
        elif 'Operator' in token_str:
            return 'operator'
        elif 'Punctuation' in token_str:
            return 'bracket'
        elif 'Name' in token_str:
            return 'variable'
        return None

    def advance_position(self, pos, text):
        """تقدم الموضع في النص"""
        line, col = pos.split('.')
        line, col = int(line), int(col)

        for char in text:
            if char == '\n':
                line += 1
                col = 0
            else:
                col += 1

        return f"{line}.{col}"

    def simple_highlight(self):
        """تلوين بسيط في حالة فشل pygments"""
        content = self.text_area.get(1.0, tk.END)

        # تلوين الكلمات المفتاحية
        keywords = ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except',
                   'finally', 'with', 'import', 'from', 'as', 'return', 'yield', 'lambda',
                   'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None', 'pass', 'break',
                   'continue', 'global', 'nonlocal', 'assert', 'del', 'raise']

        for keyword in keywords:
            self.highlight_pattern(r'\b' + keyword + r'\b', 'keyword')

        # تلوين النصوص
        self.highlight_pattern(r'"[^"]*?"', 'string')
        self.highlight_pattern(r"'[^']*?'", 'string')
        self.highlight_pattern(r'""".*?"""', 'string')
        self.highlight_pattern(r"'''.*?'''", 'string')

        # تلوين التعليقات
        self.highlight_pattern(r'#.*', 'comment')

        # تلوين الأرقام
        self.highlight_pattern(r'\b\d+\.?\d*\b', 'number')

        # تلوين الدوال المدمجة
        builtins = ['print', 'len', 'range', 'str', 'int', 'float', 'list', 'dict', 'tuple',
                   'set', 'bool', 'type', 'isinstance', 'hasattr', 'getattr', 'setattr',
                   'open', 'input', 'abs', 'min', 'max', 'sum', 'all', 'any', 'enumerate',
                   'zip', 'map', 'filter', 'sorted', 'reversed']

        for builtin in builtins:
            self.highlight_pattern(r'\b' + builtin + r'\b', 'builtin')

        # تلوين أسماء الدوال
        self.highlight_pattern(r'def\s+(\w+)', 'function', group=1)

        # تلوين أسماء الكلاسات
        self.highlight_pattern(r'class\s+(\w+)', 'class', group=1)

        # تلوين الأقواس والعمليات
        self.highlight_pattern(r'[(){}[\]]', 'bracket')
        self.highlight_pattern(r'[+\-*/%=<>!&|^~]', 'operator')
        
    def highlight_pattern(self, pattern, tag, group=0):
        """تلوين نمط معين في النص"""
        content = self.text_area.get(1.0, tk.END)
        
        for match in re.finditer(pattern, content, re.MULTILINE | re.DOTALL):
            start_pos = self.get_text_position(content, match.start(group))
            end_pos = self.get_text_position(content, match.end(group))
            self.text_area.tag_add(tag, start_pos, end_pos)
    
    def get_text_position(self, content, index):
        """تحويل فهرس النص إلى موضع في Text widget"""
        lines_before = content[:index].count('\n')
        line_start = content.rfind('\n', 0, index) + 1
        column = index - line_start
        return f"{lines_before + 1}.{column}"
        
    def open_python_file(self):
        """فتح ملف Python وعرض محتواه"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # مسح المحتوى السابق وإدراج الجديد
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(1.0, content)
                
                # تحديث معلومات الملف
                self.current_file = file_path
                filename = os.path.basename(file_path)
                self.file_label.config(text=f"الملف: {filename}")
                
                # تلوين الكود تلقائياً
                self.highlight_syntax()

                # فحص متطلبات المشروع تلقائياً
                self.check_project_requirements()

                # التثبيت التلقائي للمتطلبات المفقودة
                self.auto_install_missing_requirements()

            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح الملف:\n{str(e)}")

    # ==================== دوال إدارة المكتبات ====================

    def toggle_library_menu(self):
        """إظهار/إخفاء قائمة إدارة المكتبات"""
        if self.library_menu_visible:
            self.library_menu_frame.pack_forget()
            self.library_menu_visible = False
        else:
            self.library_menu_frame.pack(fill=tk.X, pady=(5, 0))
            self.library_menu_visible = True

    def show_results_window(self):
        """إظهار نافذة النتائج"""
        self.results_frame.pack(fill=tk.BOTH, expand=False, pady=(10, 0))
        self.results_text.delete(1.0, tk.END)

    def hide_results_window(self):
        """إخفاء نافذة النتائج"""
        self.results_frame.pack_forget()

    def add_result_text(self, text, color="white"):
        """إضافة نص إلى نافذة النتائج"""
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)
        self.root.update()

    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress_var.set(value)
        self.root.update()

    # ==================== دوال إدارة ملف requirements.txt ====================

    def get_project_directory(self):
        """الحصول على مجلد المشروع"""
        if not self.current_file:
            return None
        return os.path.dirname(self.current_file)

    def get_requirements_path(self):
        """الحصول على مسار ملف requirements.txt"""
        project_dir = self.get_project_directory()
        if not project_dir:
            return None
        return os.path.join(project_dir, "requirements.txt")

    def read_requirements_file(self):
        """قراءة ملف requirements.txt"""
        requirements_path = self.get_requirements_path()
        if not requirements_path or not os.path.exists(requirements_path):
            return []

        try:
            with open(requirements_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            requirements = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # إزالة معلومات الإصدار للحصول على اسم المكتبة فقط
                    lib_name = line.split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0]
                    requirements.append(lib_name.strip())

            return requirements
        except Exception as e:
            self.add_result_text(f"خطأ في قراءة requirements.txt: {str(e)}")
            return []

    def write_requirements_file(self, libraries):
        """كتابة ملف requirements.txt محسن"""
        requirements_path = self.get_requirements_path()
        if not requirements_path:
            self.add_result_text("❌ لا يمكن تحديد مسار ملف requirements.txt")
            return False

        try:
            # الحصول على إصدارات المكتبات المثبتة
            self.add_result_text("📋 جاري الحصول على إصدارات المكتبات...")
            installed_versions = self.get_installed_versions(libraries)

            # إنشاء محتوى الملف
            content_lines = []
            content_lines.append("# ===================================================")
            content_lines.append("# متطلبات المشروع - تم إنشاؤها تلقائياً")
            content_lines.append(f"# تاريخ الإنشاء: {self.get_current_datetime()}")
            content_lines.append(f"# ملف Python: {os.path.basename(self.current_file) if self.current_file else 'غير محدد'}")
            content_lines.append("# ===================================================")
            content_lines.append("")

            # تصنيف المكتبات
            core_libs = ['requests', 'urllib3', 'certifi']
            data_libs = ['pandas', 'numpy', 'scipy']
            viz_libs = ['matplotlib', 'seaborn', 'plotly']
            ml_libs = ['sklearn', 'tensorflow', 'torch', 'keras']
            web_libs = ['flask', 'django', 'fastapi']
            other_libs = []

            # تصنيف المكتبات
            categorized = {
                'مكتبات أساسية': [],
                'مكتبات البيانات': [],
                'مكتبات الرسوم البيانية': [],
                'مكتبات التعلم الآلي': [],
                'مكتبات تطوير الويب': [],
                'مكتبات أخرى': []
            }

            for lib in sorted(libraries):
                version = installed_versions.get(lib, "")
                lib_entry = f"{lib}=={version}" if version else lib

                if lib in core_libs:
                    categorized['مكتبات أساسية'].append(lib_entry)
                elif lib in data_libs:
                    categorized['مكتبات البيانات'].append(lib_entry)
                elif lib in viz_libs:
                    categorized['مكتبات الرسوم البيانية'].append(lib_entry)
                elif lib in ml_libs:
                    categorized['مكتبات التعلم الآلي'].append(lib_entry)
                elif lib in web_libs:
                    categorized['مكتبات تطوير الويب'].append(lib_entry)
                else:
                    categorized['مكتبات أخرى'].append(lib_entry)

            # كتابة المكتبات مصنفة
            for category, libs in categorized.items():
                if libs:
                    content_lines.append(f"# {category}")
                    content_lines.extend(libs)
                    content_lines.append("")

            # إضافة تعليمات التثبيت
            content_lines.append("# ===================================================")
            content_lines.append("# تعليمات التثبيت:")
            content_lines.append("# pip install -r requirements.txt")
            content_lines.append("# ===================================================")

            # كتابة الملف
            with open(requirements_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content_lines))

            self.add_result_text(f"✅ تم إنشاء ملف requirements.txt بنجاح")
            self.add_result_text(f"📁 المسار: {requirements_path}")
            self.add_result_text(f"📊 عدد المكتبات: {len(libraries)}")

            return True

        except Exception as e:
            self.add_result_text(f"❌ خطأ في كتابة requirements.txt: {str(e)}")
            return False

    def get_installed_versions(self, libraries):
        """الحصول على إصدارات المكتبات المثبتة"""
        versions = {}
        try:
            result = subprocess.run(
                ["pip", "show"] + libraries,
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            current_lib = None
            for line in result.stdout.split('\n'):
                if line.startswith('Name: '):
                    current_lib = line.replace('Name: ', '').strip()
                elif line.startswith('Version: ') and current_lib:
                    versions[current_lib] = line.replace('Version: ', '').strip()

        except Exception:
            pass

        return versions

    def get_current_datetime(self):
        """الحصول على التاريخ والوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def check_project_requirements(self):
        """فحص متطلبات المشروع تلقائياً"""
        if not self.current_file:
            return

        def check_thread():
            try:
                requirements = self.read_requirements_file()
                if not requirements:
                    self.requirements_status_label.config(
                        text="📄 لا يوجد ملف requirements.txt",
                        foreground="gray"
                    )
                    return

                # فحص المكتبات المثبتة
                missing_libs = []
                installed_libs = []

                for lib in requirements:
                    if self.is_library_installed(lib):
                        installed_libs.append(lib)
                    else:
                        missing_libs.append(lib)

                # تحديث شريط الحالة
                if missing_libs:
                    status_text = f"⚠️ مكتبات مفقودة: {len(missing_libs)} | مثبتة: {len(installed_libs)}"
                    self.requirements_status_label.config(
                        text=status_text,
                        foreground="orange"
                    )
                else:
                    status_text = f"✅ جميع المتطلبات مثبتة ({len(installed_libs)} مكتبة)"
                    self.requirements_status_label.config(
                        text=status_text,
                        foreground="green"
                    )

            except Exception as e:
                self.requirements_status_label.config(
                    text=f"❌ خطأ في فحص المتطلبات",
                    foreground="red"
                )

        threading.Thread(target=check_thread, daemon=True).start()

    def is_library_installed(self, library_name):
        """التحقق من تثبيت مكتبة"""
        try:
            result = subprocess.run(
                ["pip", "show", library_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            return result.returncode == 0
        except:
            return False

    def ai_analyze_and_install(self):
        """تحليل الكود وتثبيت المكتبات المطلوبة"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return

        code_content = self.text_area.get(1.0, tk.END)
        if not code_content.strip():
            messagebox.showwarning("تحذير", "لا يوجد كود لتحليله")
            return

        def analyze_thread():
            try:
                self.show_results_window()
                self.add_result_text("🔍 تحليل الكود للعثور على المكتبات المطلوبة...")
                self.update_progress(10)

                # تحليل الكود
                required_libraries = self.analyze_code_for_imports(code_content)
                self.add_result_text(f"📊 تم اكتشاف {len(required_libraries)} مكتبة من الكود")

                existing_requirements = self.read_requirements_file()
                self.add_result_text(f"📄 تم العثور على {len(existing_requirements)} مكتبة في requirements.txt")

                # استخراج أسماء المكتبات من النتائج
                required_lib_names = [lib['name'] for lib in required_libraries]

                # دمج المكتبات المطلوبة مع المتطلبات الموجودة
                all_libraries = list(set(required_lib_names + existing_requirements))
                self.add_result_text(f"🔗 إجمالي المكتبات المطلوبة: {len(all_libraries)}")

                self.update_progress(30)

                if not required_libraries and not existing_requirements:
                    self.add_result_text("✅ لم يتم العثور على مكتبات مطلوبة")
                    self.update_progress(100)
                    return

                # فحص حالة التثبيت لكل مكتبة
                library_status = {}

                # إضافة المكتبات المكتشفة من الكود
                for lib_info in required_libraries:
                    lib_name = lib_info['name']
                    is_installed = self.is_library_installed(lib_name)
                    library_status[lib_name] = {
                        'installed': is_installed,
                        'description': lib_info['description']
                    }

                # إضافة المكتبات الموجودة في requirements.txt
                for lib_name in existing_requirements:
                    if lib_name not in library_status:
                        is_installed = self.is_library_installed(lib_name)
                        library_status[lib_name] = {
                            'installed': is_installed,
                            'description': 'مكتبة من متطلبات المشروع'
                        }

                self.update_progress(60)

                # إظهار نافذة التثبيت
                self.show_installation_window(library_status, all_libraries)

                self.update_progress(100)

            except Exception as e:
                self.add_result_text(f"❌ خطأ في التحليل: {str(e)}")
                self.add_result_text(f"📝 تفاصيل الخطأ: {type(e).__name__}")
                # إضافة معلومات تشخيصية
                import traceback
                self.add_result_text("🔍 تفاصيل تقنية:")
                self.add_result_text(traceback.format_exc())
            finally:
                self.update_progress(0)

        threading.Thread(target=analyze_thread, daemon=True).start()

    def show_installation_window(self, library_status, all_libraries):
        """إظهار نافذة تثبيت المكتبات"""
        install_window = tk.Toplevel(self.root)
        install_window.title("تحليل وتثبيت المكتبات")
        install_window.geometry("700x500")
        install_window.resizable(True, True)

        # العنوان
        title_frame = ttk.Frame(install_window)
        title_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(title_frame, text="المكتبات المطلوبة للمشروع",
                 font=("Arial", 14, "bold")).pack()

        # إطار القائمة
        list_frame = ttk.LabelFrame(install_window, text="حالة المكتبات")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # إنشاء Treeview لعرض المكتبات
        columns = ("المكتبة", "الحالة", "الوصف")
        tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        tree.heading("المكتبة", text="اسم المكتبة")
        tree.heading("الحالة", text="حالة التثبيت")
        tree.heading("الوصف", text="الوصف")

        # تعيين عرض الأعمدة
        tree.column("المكتبة", width=150)
        tree.column("الحالة", width=120)
        tree.column("الوصف", width=300)

        # شريط تمرير
        tree_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=tree_scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # متغيرات الاختيار
        self.library_vars = {}

        # إضافة المكتبات إلى الجدول
        for lib_name, status in library_status.items():
            status_text = "✅ مثبتة" if status['installed'] else "❌ غير مثبتة"
            status_color = "green" if status['installed'] else "red"

            item_id = tree.insert("", tk.END, values=(
                lib_name,
                status_text,
                status['description']
            ))

            # تلوين الصف حسب الحالة
            if not status['installed']:
                tree.set(item_id, "الحالة", "❌ غير مثبتة")

            # إنشاء متغير اختيار للمكتبات غير المثبتة
            if not status['installed']:
                var = tk.BooleanVar(value=True)
                self.library_vars[lib_name] = var

        # إطار الخيارات
        options_frame = ttk.Frame(install_window)
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # خيارات التثبيت
        ttk.Label(options_frame, text="خيارات التثبيت:", font=("Arial", 10, "bold")).pack(anchor=tk.W)

        select_frame = ttk.Frame(options_frame)
        select_frame.pack(fill=tk.X, pady=(5, 10))

        # أزرار التحديد
        ttk.Button(select_frame, text="تحديد الكل",
                  command=lambda: self.select_all_libraries(True)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(select_frame, text="إلغاء تحديد الكل",
                  command=lambda: self.select_all_libraries(False)).pack(side=tk.LEFT)

        # قائمة المكتبات مع checkboxes للمكتبات غير المثبتة
        if self.library_vars:
            checkbox_frame = ttk.LabelFrame(options_frame, text="اختر المكتبات للتثبيت")
            checkbox_frame.pack(fill=tk.X, pady=(0, 10))

            for lib_name, var in self.library_vars.items():
                ttk.Checkbutton(checkbox_frame, text=f"{lib_name}: {library_status[lib_name]['description']}",
                               variable=var).pack(anchor=tk.W, padx=5, pady=2)

        # أزرار العمل
        buttons_frame = ttk.Frame(install_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(buttons_frame, text="تثبيت المحدد",
                  command=lambda: self.install_selected_libraries_and_update_requirements(
                      install_window, all_libraries)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تحديث requirements.txt فقط",
                  command=lambda: self.update_requirements_only(all_libraries)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء",
                  command=install_window.destroy).pack(side=tk.RIGHT)

    def select_all_libraries(self, select_all):
        """تحديد/إلغاء تحديد جميع المكتبات"""
        for var in self.library_vars.values():
            var.set(select_all)

    def install_selected_libraries_and_update_requirements(self, window, all_libraries):
        """تثبيت المكتبات المحددة وتحديث requirements.txt"""
        selected_libraries = []
        for lib_name, var in self.library_vars.items():
            if var.get():
                selected_libraries.append(lib_name)

        if not selected_libraries:
            messagebox.showinfo("معلومات", "لم يتم تحديد أي مكتبة للتثبيت")
            return

        window.destroy()

        # تثبيت المكتبات المحددة
        def install_and_update():
            try:
                self.show_results_window()
                self.add_result_text("🚀 بدء تثبيت المكتبات المحددة...")

                success_count = 0
                for i, lib_name in enumerate(selected_libraries):
                    progress = int((i / len(selected_libraries)) * 80)
                    self.update_progress(progress)

                    self.add_result_text(f"تثبيت {lib_name}...")
                    if self.install_library_sync(lib_name):
                        success_count += 1
                        self.add_result_text(f"✅ تم تثبيت {lib_name} بنجاح")
                    else:
                        self.add_result_text(f"❌ فشل في تثبيت {lib_name}")

                self.update_progress(90)

                # تحديث requirements.txt
                if self.write_requirements_file(all_libraries):
                    self.add_result_text("📝 تم تحديث ملف requirements.txt")
                else:
                    self.add_result_text("⚠️ فشل في تحديث ملف requirements.txt")

                self.update_progress(100)

                # رسالة النجاح النهائية
                if success_count == len(selected_libraries):
                    self.add_result_text("🎉 تم تثبيت جميع المكتبات بنجاح!")
                    self.add_result_text("✅ جميع متطلبات المشروع متوفرة الآن")
                    messagebox.showinfo("نجح التثبيت", "تم تثبيت جميع المكتبات المطلوبة بنجاح!")
                else:
                    self.add_result_text(f"⚠️ انتهى التثبيت! نجح: {success_count}/{len(selected_libraries)}")
                    if success_count > 0:
                        messagebox.showwarning("تثبيت جزئي", f"تم تثبيت {success_count} من {len(selected_libraries)} مكتبة")

                # إعادة فحص المتطلبات
                self.check_project_requirements()

            except Exception as e:
                self.add_result_text(f"خطأ: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=install_and_update, daemon=True).start()

    def update_requirements_only(self, all_libraries):
        """تحديث ملف requirements.txt فقط"""
        if self.write_requirements_file(all_libraries):
            self.show_results_window()
            self.add_result_text("📝 تم تحديث ملف requirements.txt بنجاح")
            self.check_project_requirements()
            messagebox.showinfo("نجح", "تم تحديث ملف requirements.txt")
        else:
            messagebox.showerror("خطأ", "فشل في تحديث ملف requirements.txt")

    def install_library_sync(self, library_name):
        """تثبيت مكتبة بشكل متزامن مع الذكاء الاصطناعي لحل المشاكل"""
        try:
            # المحاولة الأولى للتثبيت
            self.add_result_text(f"🔄 محاولة تثبيت {library_name}...")
            result = subprocess.run(
                ["pip", "install", library_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                self.add_result_text(f"✅ تم تثبيت {library_name} بنجاح")
                return True
            else:
                # فشل التثبيت - تفعيل الذكاء الاصطناعي
                self.add_result_text(f"⚠️ فشل تثبيت {library_name} - تفعيل الذكاء الاصطناعي...")
                return self.ai_fix_installation_problem(library_name, result.stderr)

        except Exception as e:
            self.add_result_text(f"❌ خطأ في تثبيت {library_name}: {str(e)}")
            return False

    def ai_fix_installation_problem(self, library_name, error_message):
        """الذكاء الاصطناعي لحل مشاكل التثبيت"""
        self.add_result_text(f"🤖 الذكاء الاصطناعي يحلل مشكلة {library_name}...")

        # قاعدة بيانات المشاكل الشائعة والحلول
        common_fixes = {
            'sklearn': ['scikit-learn'],
            'cv2': ['opencv-python'],
            'PIL': ['Pillow'],
            'bs4': ['beautifulsoup4'],
            'yaml': ['PyYAML'],
            'dotenv': ['python-dotenv'],
            'jwt': ['PyJWT'],
            'crypto': ['pycryptodome'],
            'dateutil': ['python-dateutil'],
            'magic': ['python-magic-bin'],
            'win32': ['pywin32'],
            'psutil': ['psutil'],
            'lxml': ['lxml'],
            'xlrd': ['xlrd'],
            'openpyxl': ['openpyxl'],
            'matplotlib': ['matplotlib'],
            'seaborn': ['seaborn'],
            'plotly': ['plotly'],
            'dash': ['dash'],
            'streamlit': ['streamlit'],
            'gradio': ['gradio']
        }

        # تحليل رسالة الخطأ
        error_lower = error_message.lower() if error_message else ""

        # الحل 1: اسم المكتبة البديل
        if library_name in common_fixes:
            for alternative in common_fixes[library_name]:
                self.add_result_text(f"🔧 جاري تجربة الاسم البديل: {alternative}")
                if self.try_install_alternative(alternative):
                    return True

        # الحل 2: تحديث pip
        if 'outdated' in error_lower or 'upgrade' in error_lower:
            self.add_result_text("🔧 تحديث pip...")
            if self.upgrade_pip():
                return self.try_install_alternative(library_name)

        # الحل 3: تثبيت من مصدر مختلف
        if 'not found' in error_lower or '404' in error_lower:
            self.add_result_text("🔧 البحث عن مصادر بديلة...")
            alternatives = self.find_alternative_sources(library_name)
            for alt in alternatives:
                if self.try_install_alternative(alt):
                    return True

        # الحل 4: تثبيت التبعيات المطلوبة
        if 'dependency' in error_lower or 'requires' in error_lower:
            self.add_result_text("🔧 تثبيت التبعيات المطلوبة...")
            if self.install_dependencies(library_name):
                return self.try_install_alternative(library_name)

        # الحل 5: تثبيت بخيارات إضافية
        if 'compiler' in error_lower or 'build' in error_lower:
            self.add_result_text("🔧 تثبيت بخيارات إضافية...")
            return self.try_install_with_options(library_name)

        # الحل الأخير: البحث الذكي
        self.add_result_text("🔧 البحث الذكي عن حلول...")
        return self.smart_search_and_install(library_name)

    def try_install_alternative(self, library_name):
        """تجربة تثبيت مكتبة بديلة"""
        try:
            result = subprocess.run(
                ["pip", "install", library_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                self.add_result_text(f"✅ نجح تثبيت {library_name}")
                return True
            else:
                self.add_result_text(f"❌ فشل تثبيت {library_name}")
                return False
        except:
            return False

    def upgrade_pip(self):
        """تحديث pip"""
        try:
            self.add_result_text("🔄 تحديث pip...")
            result = subprocess.run(
                ["python", "-m", "pip", "install", "--upgrade", "pip"],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                self.add_result_text("✅ تم تحديث pip بنجاح")
                return True
            else:
                self.add_result_text("❌ فشل في تحديث pip")
                return False
        except:
            return False

    def find_alternative_sources(self, library_name):
        """البحث عن مصادر بديلة للمكتبة"""
        alternatives = []

        # مصادر بديلة شائعة
        common_alternatives = {
            'tensorflow': ['tensorflow-cpu', 'tensorflow-gpu'],
            'torch': ['torch', 'pytorch'],
            'opencv': ['opencv-python', 'opencv-contrib-python'],
            'pillow': ['Pillow', 'PIL'],
            'yaml': ['PyYAML', 'ruamel.yaml'],
            'crypto': ['pycryptodome', 'cryptography'],
            'lxml': ['lxml', 'beautifulsoup4'],
            'psycopg2': ['psycopg2-binary', 'psycopg2']
        }

        # البحث في القاموس
        for key, alts in common_alternatives.items():
            if key.lower() in library_name.lower():
                alternatives.extend(alts)

        # إضافة تنويعات الاسم
        alternatives.append(library_name.lower())
        alternatives.append(library_name.upper())
        alternatives.append(library_name.replace('-', '_'))
        alternatives.append(library_name.replace('_', '-'))

        return list(set(alternatives))

    def install_dependencies(self, library_name):
        """تثبيت التبعيات المطلوبة"""
        dependencies = {
            'lxml': ['libxml2-dev', 'libxslt-dev'],
            'psycopg2': ['postgresql-dev'],
            'mysql-python': ['mysql-dev'],
            'pillow': ['libjpeg-dev', 'zlib1g-dev'],
            'matplotlib': ['python3-tk'],
            'opencv-python': ['libgl1-mesa-glx']
        }

        if library_name in dependencies:
            self.add_result_text(f"🔧 تثبيت التبعيات لـ {library_name}...")
            # في Windows، معظم التبعيات تأتي مع المكتبة
            return True

        return False

    def try_install_with_options(self, library_name):
        """تثبيت مع خيارات إضافية"""
        options_to_try = [
            ["pip", "install", library_name, "--no-cache-dir"],
            ["pip", "install", library_name, "--user"],
            ["pip", "install", library_name, "--upgrade"],
            ["pip", "install", library_name, "--force-reinstall"],
            ["pip", "install", library_name, "--no-deps"],
            ["pip", "install", library_name, "--pre"]
        ]

        for options in options_to_try:
            try:
                self.add_result_text(f"🔧 تجربة: {' '.join(options)}")
                result = subprocess.run(
                    options,
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                if result.returncode == 0:
                    self.add_result_text(f"✅ نجح التثبيت بالخيارات: {' '.join(options[2:])}")
                    return True

            except:
                continue

        return False

    def smart_search_and_install(self, library_name):
        """البحث الذكي والتثبيت"""
        self.add_result_text(f"🔍 البحث الذكي عن {library_name}...")

        # قائمة بدائل ذكية
        smart_alternatives = {
            'sklearn': 'scikit-learn',
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'bs4': 'beautifulsoup4',
            'yaml': 'PyYAML',
            'dotenv': 'python-dotenv',
            'jwt': 'PyJWT',
            'requests': 'requests',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'seaborn': 'seaborn',
            'flask': 'Flask',
            'django': 'Django',
            'fastapi': 'fastapi',
            'streamlit': 'streamlit',
            'gradio': 'gradio',
            'plotly': 'plotly',
            'dash': 'dash'
        }

        # البحث في البدائل الذكية
        if library_name in smart_alternatives:
            alternative = smart_alternatives[library_name]
            self.add_result_text(f"💡 تم العثور على بديل ذكي: {alternative}")
            return self.try_install_alternative(alternative)

        # البحث بالتشابه
        for key, value in smart_alternatives.items():
            if key.lower() in library_name.lower() or library_name.lower() in key.lower():
                self.add_result_text(f"💡 تم العثور على بديل مشابه: {value}")
                return self.try_install_alternative(value)

        # المحاولة الأخيرة مع conda
        return self.try_conda_install(library_name)

    def try_conda_install(self, library_name):
        """محاولة التثبيت باستخدام conda"""
        try:
            self.add_result_text(f"🐍 محاولة تثبيت {library_name} باستخدام conda...")
            result = subprocess.run(
                ["conda", "install", "-y", library_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                self.add_result_text(f"✅ تم تثبيت {library_name} بنجاح باستخدام conda")
                return True
            else:
                self.add_result_text(f"❌ فشل تثبيت {library_name} باستخدام conda")
                return False

        except FileNotFoundError:
            self.add_result_text("⚠️ conda غير متوفر في النظام")
            return False
        except:
            return False

    def auto_install_missing_requirements(self):
        """التثبيت التلقائي للمتطلبات المفقودة"""
        if not self.current_file:
            return

        def auto_install_thread():
            try:
                requirements = self.read_requirements_file()
                if not requirements:
                    return

                # فحص المكتبات المفقودة
                missing_libs = []
                for lib in requirements:
                    if not self.is_library_installed(lib):
                        missing_libs.append(lib)

                if not missing_libs:
                    return

                # سؤال المستخدم عن التثبيت التلقائي
                result = messagebox.askyesno(
                    "تثبيت تلقائي",
                    f"تم العثور على {len(missing_libs)} مكتبة مفقودة في requirements.txt\n"
                    f"هل تريد تثبيتها تلقائياً؟\n\n"
                    f"المكتبات المفقودة: {', '.join(missing_libs[:5])}"
                    f"{'...' if len(missing_libs) > 5 else ''}"
                )

                if not result:
                    return

                # بدء التثبيت التلقائي
                self.show_results_window()
                self.add_result_text("🚀 بدء التثبيت التلقائي للمتطلبات المفقودة...")
                self.add_result_text(f"📋 عدد المكتبات المطلوبة: {len(missing_libs)}")

                success_count = 0
                failed_libs = []

                for i, lib in enumerate(missing_libs):
                    progress = int((i / len(missing_libs)) * 90)
                    self.update_progress(progress)

                    self.add_result_text(f"📦 تثبيت {lib}... ({i+1}/{len(missing_libs)})")

                    if self.install_library_sync(lib):
                        success_count += 1
                    else:
                        failed_libs.append(lib)

                self.update_progress(100)

                # تقرير النتائج
                self.add_result_text("=" * 50)
                self.add_result_text("📊 تقرير التثبيت التلقائي:")
                self.add_result_text(f"✅ نجح: {success_count}/{len(missing_libs)}")

                if failed_libs:
                    self.add_result_text(f"❌ فشل: {len(failed_libs)}")
                    self.add_result_text(f"المكتبات الفاشلة: {', '.join(failed_libs)}")

                if success_count == len(missing_libs):
                    self.add_result_text("🎉 تم تثبيت جميع المكتبات بنجاح!")
                    messagebox.showinfo("نجح", "تم تثبيت جميع المكتبات المطلوبة بنجاح!")
                else:
                    self.add_result_text("⚠️ تم تثبيت بعض المكتبات فقط")

                # إعادة فحص المتطلبات
                self.check_project_requirements()

            except Exception as e:
                self.add_result_text(f"❌ خطأ في التثبيت التلقائي: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=auto_install_thread, daemon=True).start()

    def find_alternative_sources(self, library_name):
        """البحث عن مصادر بديلة للمكتبة"""
        alternatives = []

        # مصادر بديلة شائعة
        common_alternatives = {
            'tensorflow': ['tensorflow-cpu', 'tensorflow-gpu'],
            'torch': ['torch', 'pytorch'],
            'opencv': ['opencv-python', 'opencv-contrib-python'],
            'pillow': ['Pillow', 'PIL'],
            'yaml': ['PyYAML', 'ruamel.yaml'],
            'crypto': ['pycryptodome', 'cryptography'],
            'lxml': ['lxml', 'beautifulsoup4'],
            'psycopg2': ['psycopg2-binary', 'psycopg2']
        }

        # البحث في القاموس
        for key, alts in common_alternatives.items():
            if key.lower() in library_name.lower():
                alternatives.extend(alts)

        # إضافة تنويعات الاسم
        alternatives.append(library_name.lower())
        alternatives.append(library_name.upper())
        alternatives.append(library_name.replace('-', '_'))
        alternatives.append(library_name.replace('_', '-'))

        return list(set(alternatives))

    def install_dependencies(self, library_name):
        """تثبيت التبعيات المطلوبة"""
        dependencies = {
            'lxml': ['libxml2-dev', 'libxslt-dev'],
            'psycopg2': ['postgresql-dev'],
            'mysql-python': ['mysql-dev'],
            'pillow': ['libjpeg-dev', 'zlib1g-dev'],
            'matplotlib': ['python3-tk'],
            'opencv-python': ['libgl1-mesa-glx']
        }

        if library_name in dependencies:
            self.add_result_text(f"🔧 تثبيت التبعيات لـ {library_name}...")
            # في Windows، معظم التبعيات تأتي مع المكتبة
            return True

        return False

    def try_install_with_options(self, library_name):
        """تثبيت مع خيارات إضافية"""
        options_to_try = [
            ["pip", "install", library_name, "--no-cache-dir"],
            ["pip", "install", library_name, "--user"],
            ["pip", "install", library_name, "--upgrade"],
            ["pip", "install", library_name, "--force-reinstall"],
            ["pip", "install", library_name, "--no-deps"],
            ["pip", "install", library_name, "--pre"]
        ]

        for options in options_to_try:
            try:
                self.add_result_text(f"🔧 تجربة: {' '.join(options)}")
                result = subprocess.run(
                    options,
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                if result.returncode == 0:
                    self.add_result_text(f"✅ نجح التثبيت بالخيارات: {' '.join(options[2:])}")
                    return True

            except:
                continue

        return False

    def smart_search_and_install(self, library_name):
        """البحث الذكي والتثبيت"""
        self.add_result_text(f"🔍 البحث الذكي عن {library_name}...")

        # قائمة بدائل ذكية
        smart_alternatives = {
            'sklearn': 'scikit-learn',
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'bs4': 'beautifulsoup4',
            'yaml': 'PyYAML',
            'dotenv': 'python-dotenv',
            'jwt': 'PyJWT',
            'requests': 'requests',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'seaborn': 'seaborn',
            'flask': 'Flask',
            'django': 'Django',
            'fastapi': 'fastapi',
            'streamlit': 'streamlit',
            'gradio': 'gradio',
            'plotly': 'plotly',
            'dash': 'dash'
        }

        # البحث في البدائل الذكية
        if library_name in smart_alternatives:
            alternative = smart_alternatives[library_name]
            self.add_result_text(f"💡 تم العثور على بديل ذكي: {alternative}")
            return self.try_install_alternative(alternative)

        # البحث بالتشابه
        for key, value in smart_alternatives.items():
            if key.lower() in library_name.lower() or library_name.lower() in key.lower():
                self.add_result_text(f"💡 تم العثور على بديل مشابه: {value}")
                return self.try_install_alternative(value)

        # المحاولة الأخيرة مع conda
        return self.try_conda_install(library_name)

    def try_conda_install(self, library_name):
        """محاولة التثبيت باستخدام conda"""
        try:
            self.add_result_text(f"🐍 محاولة تثبيت {library_name} باستخدام conda...")
            result = subprocess.run(
                ["conda", "install", "-y", library_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                self.add_result_text(f"✅ تم تثبيت {library_name} بنجاح باستخدام conda")
                return True
            else:
                self.add_result_text(f"❌ فشل تثبيت {library_name} باستخدام conda")
                return False

        except FileNotFoundError:
            self.add_result_text("⚠️ conda غير متوفر في النظام")
            return False
        except:
            return False

    def browse_external_libraries(self):
        """تصفح وتثبيت مكتبات خارجية"""
        browse_window = tk.Toplevel(self.root)
        browse_window.title("تصفح المكتبات الخارجية")
        browse_window.geometry("600x500")
        browse_window.resizable(True, True)

        # إطار البحث
        search_frame = ttk.Frame(browse_window)
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(search_frame, text="البحث في PyPI:", font=("Arial", 12)).pack(anchor=tk.W)

        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, font=("Arial", 12))
        search_entry.pack(fill=tk.X, pady=(5, 10))

        search_button = ttk.Button(search_frame, text="بحث",
                                  command=lambda: self.search_external_library(search_var.get(), results_tree))
        search_button.pack()

        # إطار النتائج
        results_frame = ttk.LabelFrame(browse_window, text="نتائج البحث")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # جدول النتائج
        columns = ("الاسم", "الإصدار", "الوصف")
        results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        results_tree.heading("الاسم", text="اسم المكتبة")
        results_tree.heading("الإصدار", text="الإصدار")
        results_tree.heading("الوصف", text="الوصف")

        # تعيين عرض الأعمدة
        results_tree.column("الاسم", width=150)
        results_tree.column("الإصدار", width=100)
        results_tree.column("الوصف", width=300)

        # شريط تمرير للجدول
        tree_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_tree.yview)
        results_tree.configure(yscrollcommand=tree_scrollbar.set)

        results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الأزرار
        buttons_frame = ttk.Frame(browse_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(buttons_frame, text="تثبيت وإضافة للمشروع",
                  command=lambda: self.install_external_library(results_tree, browse_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إغلاق",
                  command=browse_window.destroy).pack(side=tk.RIGHT)

        # ربط Enter بالبحث
        search_entry.bind("<Return>", lambda e: self.search_external_library(search_var.get(), results_tree))
        search_entry.focus()

    def search_external_library(self, query, results_tree):
        """البحث عن مكتبة خارجية في PyPI"""
        if not query.strip():
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المكتبة للبحث")
            return

        # مسح النتائج السابقة
        for item in results_tree.get_children():
            results_tree.delete(item)

        def search_thread():
            try:
                self.show_results_window()
                self.add_result_text(f"🔍 البحث عن: {query}")
                self.update_progress(20)

                # البحث في PyPI
                url = f"https://pypi.org/pypi/{query}/json"
                response = requests.get(url, timeout=10)

                self.update_progress(60)

                if response.status_code == 200:
                    data = response.json()
                    info = data['info']

                    # إضافة النتيجة إلى الجدول
                    results_tree.insert("", tk.END, values=(
                        info['name'],
                        info['version'],
                        info['summary'][:100] + "..." if len(info['summary']) > 100 else info['summary']
                    ))

                    self.add_result_text(f"✅ تم العثور على المكتبة: {info['name']}")
                else:
                    self.add_result_text(f"❌ لم يتم العثور على مكتبة بالاسم: {query}")

                self.update_progress(100)

            except requests.exceptions.RequestException as e:
                self.add_result_text(f"❌ خطأ في الاتصال: {str(e)}")
            except Exception as e:
                self.add_result_text(f"❌ خطأ: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=search_thread, daemon=True).start()

    def install_external_library(self, results_tree, window):
        """تثبيت مكتبة خارجية وإضافتها للمشروع"""
        selection = results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مكتبة من القائمة")
            return

        item = results_tree.item(selection[0])
        library_name = item['values'][0]

        result = messagebox.askyesno("تأكيد", f"هل تريد تثبيت المكتبة: {library_name}؟")
        if result:
            window.destroy()

            def install_and_add():
                try:
                    self.show_results_window()
                    self.add_result_text(f"🚀 تثبيت المكتبة الخارجية: {library_name}")

                    if self.install_library_sync(library_name):
                        self.add_result_text(f"✅ تم تثبيت {library_name} بنجاح")

                        # إضافة المكتبة إلى requirements.txt
                        existing_requirements = self.read_requirements_file()
                        if library_name not in existing_requirements:
                            existing_requirements.append(library_name)
                            if self.write_requirements_file(existing_requirements):
                                self.add_result_text(f"📝 تم إضافة {library_name} إلى requirements.txt")
                            else:
                                self.add_result_text(f"⚠️ فشل في تحديث requirements.txt")

                        # إعادة فحص المتطلبات
                        self.check_project_requirements()

                    else:
                        self.add_result_text(f"❌ فشل في تثبيت {library_name}")

                except Exception as e:
                    self.add_result_text(f"❌ خطأ: {str(e)}")

            threading.Thread(target=install_and_add, daemon=True).start()

    def uninstall_project_library(self):
        """حذف مكتبة من المشروع"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return

        # قراءة متطلبات المشروع
        project_requirements = self.read_requirements_file()
        if not project_requirements:
            messagebox.showinfo("معلومات", "لا توجد متطلبات مشروع في ملف requirements.txt")
            return

        # إنشاء نافذة اختيار المكتبة
        uninstall_window = tk.Toplevel(self.root)
        uninstall_window.title("حذف مكتبة من المشروع")
        uninstall_window.geometry("500x400")

        ttk.Label(uninstall_window, text="اختر المكتبة المراد حذفها من المشروع:",
                 font=("Arial", 12)).pack(pady=10)

        # قائمة المكتبات
        listbox_frame = ttk.Frame(uninstall_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        listbox = tk.Listbox(listbox_frame, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        # إضافة مكتبات المشروع مع حالة التثبيت
        for lib in project_requirements:
            status = "✅ مثبتة" if self.is_library_installed(lib) else "❌ غير مثبتة"
            listbox.insert(tk.END, f"{lib} ({status})")

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار
        buttons_frame = ttk.Frame(uninstall_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def perform_project_uninstall():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار مكتبة")
                return

            selected_text = listbox.get(selection[0])
            library_name = selected_text.split(" (")[0]

            result = messagebox.askyesno("تأكيد",
                f"هل تريد حذف المكتبة '{library_name}' من المشروع؟\n"
                "سيتم حذفها من النظام وإزالتها من requirements.txt")

            if result:
                uninstall_window.destroy()
                self.perform_project_library_uninstall(library_name, project_requirements)

        ttk.Button(buttons_frame, text="حذف من المشروع",
                  command=perform_project_uninstall).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء",
                  command=uninstall_window.destroy).pack(side=tk.RIGHT)

    def perform_project_library_uninstall(self, library_name, project_requirements):
        """تنفيذ حذف مكتبة من المشروع"""
        def uninstall_thread():
            try:
                self.show_results_window()
                self.add_result_text(f"🗑️ حذف المكتبة من المشروع: {library_name}")
                self.update_progress(10)

                # حذف المكتبة من النظام
                process = subprocess.Popen(
                    ["pip", "uninstall", library_name, "-y"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                self.update_progress(50)
                stdout, stderr = process.communicate()

                # إزالة المكتبة من requirements.txt
                updated_requirements = [lib for lib in project_requirements if lib != library_name]

                self.update_progress(80)

                if self.write_requirements_file(updated_requirements):
                    self.add_result_text(f"📝 تم تحديث requirements.txt")
                else:
                    self.add_result_text(f"⚠️ فشل في تحديث requirements.txt")

                if process.returncode == 0:
                    self.add_result_text(f"✅ تم حذف {library_name} من المشروع بنجاح!")
                else:
                    self.add_result_text(f"❌ فشل في حذف {library_name}")
                    self.add_result_text(stderr)

                self.update_progress(100)

                # إعادة فحص المتطلبات
                self.check_project_requirements()

            except Exception as e:
                self.add_result_text(f"❌ خطأ في الحذف: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=uninstall_thread, daemon=True).start()

    def show_project_requirements(self):
        """عرض متطلبات المشروع"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return

        def show_requirements_thread():
            try:
                self.show_results_window()
                self.add_result_text("📋 متطلبات المشروع الحالي:")
                self.add_result_text("=" * 50)

                requirements = self.read_requirements_file()
                if not requirements:
                    self.add_result_text("📄 لا يوجد ملف requirements.txt في مجلد المشروع")
                    return

                self.add_result_text(f"📁 مجلد المشروع: {self.get_project_directory()}")
                self.add_result_text(f"📄 ملف المتطلبات: {self.get_requirements_path()}")
                self.add_result_text("")

                installed_count = 0
                missing_count = 0

                for lib in requirements:
                    if self.is_library_installed(lib):
                        self.add_result_text(f"✅ {lib} - مثبتة")
                        installed_count += 1
                    else:
                        self.add_result_text(f"❌ {lib} - غير مثبتة")
                        missing_count += 1

                self.add_result_text("")
                self.add_result_text(f"📊 الإحصائيات:")
                self.add_result_text(f"   • إجمالي المكتبات: {len(requirements)}")
                self.add_result_text(f"   • مثبتة: {installed_count}")
                self.add_result_text(f"   • مفقودة: {missing_count}")

            except Exception as e:
                self.add_result_text(f"❌ خطأ: {str(e)}")

        threading.Thread(target=show_requirements_thread, daemon=True).start()

    def install_selected_library(self, results_tree):
        """تثبيت المكتبة المحددة"""
        selection = results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مكتبة من القائمة")
            return

        item = results_tree.item(selection[0])
        library_name = item['values'][0]

        result = messagebox.askyesno("تأكيد", f"هل تريد تثبيت المكتبة: {library_name}؟")
        if result:
            self.install_library(library_name)

    def install_library(self, library_name):
        """تثبيت مكتبة"""
        def install_thread():
            try:
                self.show_results_window()
                self.add_result_text(f"بدء تثبيت المكتبة: {library_name}")
                self.update_progress(10)

                # تنفيذ أمر التثبيت
                process = subprocess.Popen(
                    ["pip", "install", library_name],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                self.update_progress(50)

                stdout, stderr = process.communicate()

                self.update_progress(90)

                if process.returncode == 0:
                    self.add_result_text(f"✅ تم تثبيت المكتبة {library_name} بنجاح!")
                    self.add_result_text(stdout)
                else:
                    self.add_result_text(f"❌ فشل في تثبيت المكتبة {library_name}")
                    self.add_result_text(stderr)

                self.update_progress(100)

            except Exception as e:
                self.add_result_text(f"خطأ في التثبيت: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=install_thread, daemon=True).start()

    def uninstall_library(self):
        """حذف مكتبة"""
        # الحصول على قائمة المكتبات المثبتة
        def get_installed_libraries():
            try:
                result = subprocess.run(
                    ["pip", "list", "--format=json"],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if result.returncode == 0:
                    return json.loads(result.stdout)
                return []
            except:
                return []

        libraries = get_installed_libraries()
        if not libraries:
            messagebox.showerror("خطأ", "لا يمكن الحصول على قائمة المكتبات المثبتة")
            return

        # إنشاء نافذة اختيار المكتبة
        uninstall_window = tk.Toplevel(self.root)
        uninstall_window.title("حذف مكتبة")
        uninstall_window.geometry("500x400")

        ttk.Label(uninstall_window, text="اختر المكتبة المراد حذفها:",
                 font=("Arial", 12)).pack(pady=10)

        # قائمة المكتبات
        listbox_frame = ttk.Frame(uninstall_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        listbox = tk.Listbox(listbox_frame, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for lib in libraries:
            listbox.insert(tk.END, f"{lib['name']} ({lib['version']})")

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار
        buttons_frame = ttk.Frame(uninstall_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def perform_uninstall():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار مكتبة")
                return

            selected_text = listbox.get(selection[0])
            library_name = selected_text.split(" (")[0]

            result = messagebox.askyesno("تأكيد", f"هل تريد حذف المكتبة: {library_name}؟")
            if result:
                uninstall_window.destroy()
                self.perform_uninstall(library_name)

        ttk.Button(buttons_frame, text="حذف", command=perform_uninstall).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=uninstall_window.destroy).pack(side=tk.RIGHT)

    def perform_uninstall(self, library_name):
        """تنفيذ حذف المكتبة"""
        def uninstall_thread():
            try:
                self.show_results_window()
                self.add_result_text(f"بدء حذف المكتبة: {library_name}")
                self.update_progress(10)

                process = subprocess.Popen(
                    ["pip", "uninstall", library_name, "-y"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                self.update_progress(50)
                stdout, stderr = process.communicate()
                self.update_progress(90)

                if process.returncode == 0:
                    self.add_result_text(f"✅ تم حذف المكتبة {library_name} بنجاح!")
                else:
                    self.add_result_text(f"❌ فشل في حذف المكتبة {library_name}")
                    self.add_result_text(stderr)

                self.update_progress(100)

            except Exception as e:
                self.add_result_text(f"خطأ في الحذف: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=uninstall_thread, daemon=True).start()

    def show_installed_libraries(self):
        """عرض المكتبات المثبتة"""
        def get_libraries_thread():
            try:
                self.show_results_window()
                self.add_result_text("جاري الحصول على قائمة المكتبات المثبتة...")
                self.update_progress(20)

                result = subprocess.run(
                    ["pip", "list"],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                self.update_progress(80)

                if result.returncode == 0:
                    self.add_result_text("📦 المكتبات المثبتة:")
                    self.add_result_text("-" * 50)
                    self.add_result_text(result.stdout)
                else:
                    self.add_result_text("❌ فشل في الحصول على قائمة المكتبات")
                    self.add_result_text(result.stderr)

                self.update_progress(100)

            except Exception as e:
                self.add_result_text(f"خطأ: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=get_libraries_thread, daemon=True).start()

    def ai_suggest_libraries(self):
        """اقتراح المكتبات بالذكاء الاصطناعي"""
        if not self.current_file:
            messagebox.showwarning("تحذير", "يرجى فتح ملف Python أولاً")
            return

        code_content = self.text_area.get(1.0, tk.END)
        if not code_content.strip():
            messagebox.showwarning("تحذير", "لا يوجد كود لتحليله")
            return

        def analyze_code_thread():
            try:
                self.show_results_window()
                self.add_result_text("🤖 تحليل الكود باستخدام الذكاء الاصطناعي...")
                self.update_progress(10)

                # تحليل الكود للعثور على imports المفقودة
                missing_imports = self.analyze_code_for_imports(code_content)
                self.update_progress(50)

                if missing_imports:
                    self.add_result_text("📋 المكتبات المقترحة:")
                    self.add_result_text("-" * 40)

                    # إنشاء نافذة اقتراحات
                    self.show_suggestions_window(missing_imports)

                    for lib in missing_imports:
                        self.add_result_text(f"• {lib['name']}: {lib['description']}")
                else:
                    self.add_result_text("✅ لا توجد مكتبات مفقودة واضحة في الكود")

                self.update_progress(100)

            except Exception as e:
                self.add_result_text(f"خطأ في التحليل: {str(e)}")
            finally:
                self.update_progress(0)

        threading.Thread(target=analyze_code_thread, daemon=True).start()

    def analyze_code_for_imports(self, code):
        """تحليل الكود للعثور على المكتبات المطلوبة"""
        suggestions = []

        # قاموس المكتبات الشائعة وأوصافها
        common_libraries = {
            'requests': 'مكتبة لإجراء طلبات HTTP',
            'numpy': 'مكتبة للحوسبة العلمية والمصفوفات',
            'pandas': 'مكتبة لتحليل البيانات والجداول',
            'matplotlib': 'مكتبة لرسم الرسوم البيانية',
            'seaborn': 'مكتبة لرسم الرسوم البيانية المتقدمة',
            'scipy': 'مكتبة للحوسبة العلمية المتقدمة',
            'sklearn': 'مكتبة للتعلم الآلي',
            'tensorflow': 'مكتبة للتعلم العميق',
            'torch': 'مكتبة PyTorch للتعلم العميق',
            'flask': 'إطار عمل لتطوير تطبيقات الويب',
            'django': 'إطار عمل متقدم لتطوير تطبيقات الويب',
            'beautifulsoup4': 'مكتبة لتحليل HTML و XML',
            'pillow': 'مكتبة لمعالجة الصور',
            'opencv-python': 'مكتبة لمعالجة الصور والرؤية الحاسوبية',
            'sqlalchemy': 'مكتبة للتعامل مع قواعد البيانات',
            'pytest': 'مكتبة لكتابة الاختبارات',
            'click': 'مكتبة لإنشاء واجهات سطر الأوامر',
            'tqdm': 'مكتبة لإظهار شريط التقدم',
            'openpyxl': 'مكتبة للتعامل مع ملفات Excel',
            'python-dotenv': 'مكتبة لقراءة متغيرات البيئة'
        }

        # البحث عن الكلمات المفتاحية في الكود
        code_lower = code.lower()

        # تحليل الكود للبحث عن استخدامات محتملة
        patterns = {
            'requests': ['requests.', 'http', 'api', 'get(', 'post(', 'url'],
            'numpy': ['np.', 'array', 'matrix', 'numpy'],
            'pandas': ['pd.', 'dataframe', 'csv', 'read_csv', 'pandas'],
            'matplotlib': ['plt.', 'plot', 'show()', 'matplotlib'],
            'seaborn': ['sns.', 'seaborn'],
            'flask': ['flask', 'app.route', '@app.'],
            'django': ['django', 'models.model'],
            'beautifulsoup4': ['beautifulsoup', 'soup', 'html.parser'],
            'pillow': ['image.', 'pil', 'pillow'],
            'opencv-python': ['cv2', 'opencv'],
            'sqlalchemy': ['sqlalchemy', 'create_engine'],
            'pytest': ['pytest', 'test_'],
            'tqdm': ['tqdm', 'progress'],
            'openpyxl': ['openpyxl', '.xlsx', 'workbook'],
            'python-dotenv': ['dotenv', 'load_dotenv', '.env']
        }

        for lib_name, keywords in patterns.items():
            if any(keyword in code_lower for keyword in keywords):
                # التحقق من عدم وجود import للمكتبة
                if not self.check_import_exists(code, lib_name):
                    suggestions.append({
                        'name': lib_name,
                        'description': common_libraries.get(lib_name, 'مكتبة مفيدة')
                    })

        return suggestions

    def check_import_exists(self, code, library_name):
        """التحقق من وجود import للمكتبة في الكود"""
        import_patterns = [
            f'import {library_name}',
            f'from {library_name}',
            f'import {library_name.replace("-", "_")}',
            f'from {library_name.replace("-", "_")}'
        ]

        code_lines = code.lower().split('\n')
        for line in code_lines:
            line = line.strip()
            if any(pattern in line for pattern in import_patterns):
                return True
        return False

    def show_suggestions_window(self, suggestions):
        """إظهار نافذة اقتراحات المكتبات"""
        suggestions_window = tk.Toplevel(self.root)
        suggestions_window.title("اقتراحات المكتبات")
        suggestions_window.geometry("600x400")

        ttk.Label(suggestions_window, text="المكتبات المقترحة بناءً على تحليل الكود:",
                 font=("Arial", 12, "bold")).pack(pady=10)

        # إطار القائمة
        list_frame = ttk.Frame(suggestions_window)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # متغيرات الاختيار
        self.suggestion_vars = {}

        for suggestion in suggestions:
            frame = ttk.Frame(list_frame)
            frame.pack(fill=tk.X, pady=2)

            var = tk.BooleanVar(value=True)
            self.suggestion_vars[suggestion['name']] = var

            ttk.Checkbutton(frame, text=f"{suggestion['name']}: {suggestion['description']}",
                           variable=var).pack(anchor=tk.W)

        # أزرار
        buttons_frame = ttk.Frame(suggestions_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(buttons_frame, text="تثبيت المحدد",
                  command=lambda: self.install_selected_suggestions(suggestions_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء",
                  command=suggestions_window.destroy).pack(side=tk.RIGHT)

    def install_selected_suggestions(self, window):
        """تثبيت المكتبات المحددة من الاقتراحات"""
        selected_libraries = []
        for lib_name, var in self.suggestion_vars.items():
            if var.get():
                selected_libraries.append(lib_name)

        if not selected_libraries:
            messagebox.showwarning("تحذير", "لم يتم تحديد أي مكتبة")
            return

        window.destroy()

        # تثبيت المكتبات المحددة
        for lib_name in selected_libraries:
            self.install_library(lib_name)
    


def main():
    root = tk.Tk()
    app = PythonToolsApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
